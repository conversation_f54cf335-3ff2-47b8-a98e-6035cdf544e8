## 一、项目架构概览

### 1.1 技术栈
Cherry Studio 是基于 Electron + React + TypeScript 的桌面 AI 客户端：

- **框架**: Electron 37.2.3 (跨平台桌面应用)
- **前端**: React 19 + TypeScript 5.6
- **构建**: Electron-Vite 4.0 + Rolldown
- **状态管理**: Redux Toolkit + Redux Persist
- **样式**: Styled Components + Ant Design + SCSS
- **包管理**: Yarn 4.9.1 (Monorepo)
- **测试**: Vitest + Playwright

### 1.2 项目结构
```
cherry-studio/
├── packages/                   # 子包目录 (Monorepo)
│   ├── shared/                 # 跨进程共享代码
│   └── mcp-trace/              # MCP 追踪系统
├── src/                        # 主要源码
│   ├── main/                   # Electron 主进程
│   ├── preload/                # 预加载脚本
│   ├── renderer/               # 渲染进程 (React 应用)
│   └── types/                  # 全局类型定义
├── build/                      # 构建资源 (图标、配置等)
├── docs/                       # 官方文档
├── scripts/                    # 构建和部署脚本
└── tests/                      # 测试文件
```

## 二、Electron 进程架构

### 2.1 主进程 (Main Process)
```
src/main/
├── index.ts                    # 主进程入口
├── bootstrap.ts                # 应用启动逻辑
├── ipc.ts                     # IPC 通信处理
├── services/                   # 服务层
│   ├── WindowService.ts        # 窗口管理 (单例模式)
│   ├── FileSystemService.ts    # 文件系统 (静态方法)
│   ├── MCPService.ts          # MCP 协议 (单例模式)
│   └── LoggerService.ts       # 日志服务 (单例模式)
└── utils/                     # 工具函数
```

### 2.2 渲染进程 (Renderer Process)
```
src/renderer/src/
├── App.tsx                    # 应用根组件
├── Router.tsx                 # 路由配置
├── entryPoint.tsx            # 渲染进程入口
├── pages/                    # 页面组件
├── components/               # 通用组件
├── windows/                  # 窗口特定组件
│   ├── mini/                 # 迷你窗口
│   └── selection/            # 选择工具栏
├── store/                    # Redux 状态管理
├── services/                 # 渲染进程服务
├── hooks/                    # 自定义 Hooks
├── utils/                    # 工具函数
└── assets/                   # 静态资源
```

### 2.3 共享代码包 (packages/shared)
- **IPC 通道定义**: 主进程和渲染进程间的通信接口
- **类型定义**: 跨进程共享的 TypeScript 类型
- **配置文件**: 语言配置、日志配置等

## 三、服务架构模式

Cherry Studio 采用分层服务架构，不同类型的服务使用不同的设计模式：

### 3.1 主进程服务模式
- **单例模式**: WindowService, LoggerService, MCPService
- **静态方法**: FileSystemService (无状态工具类)

### 3.2 渲染进程服务模式
- **函数式服务**: ModelService (纯函数集合)
- **事件总线**: EventService (发布订阅模式)
- **API 服务**: ApiService (HTTP 请求封装)

### 3.3 跨窗口通信
- **方式1**: 共享 Redux Store (状态同步)
- **方式2**: EventService 事件总线 (松耦合通信)

## 四、 新增功能

###（一）Live2D服务

### 4.1.1 文件清单

**新建文件 **
```
src/renderer/src/
├── services/Live2DService.ts                      # Live2D 业务逻辑服务 (323行)
├── components/Live2D/
│   ├── VirtualCharacter.tsx                       # Live2D UI组件 (380行)
│   └── index.ts                                   # 组件导出 (9行)
├── store/virtualCharacter.ts                      # Redux状态管理 (104行)
└── assets/live2d/                                 # Live2D资源文件
    ├── libs/
    │   ├── live2d.min.js                         # Live2D核心库 (7160行)
    │   └── live2dcubismcore.min.js               # Cubism核心库 (11723行)
    ├── live2d.html                               # Live2D渲染容器 (642行)
    └── models/                                   # 模型文件
```

**修改文件 **
```
├── src/renderer/src/store/index.ts                     # 注册virtualCharacter slice (+2行)
├── src/renderer/src/services/EventService.ts           # 添加Live2D事件定义 (+15行)
├── src/renderer/src/windows/mini/home/<USER>
├── src/renderer/src/utils/prompt.ts                    # 添加Live2D提示词处理 (+16行)
├── src/preload/index.ts                               # 添加预加载脚本 (+9行)
├── src/main/ipc.ts                                    # 添加IPC通信处理 (+42行)
└── packages/shared/IpcChannel.ts                      # 添加IPC通道定义 (+9行)
```

### 4.1.2 组件职责分工

| 组件 | 职责 | 设计模式 | 代码行数 |
|------|------|----------|----------|
| **Live2DService** | 模型加载、动画控制、错误处理 | 函数式服务 | 323行 |
| **VirtualCharacter** | 用户交互、状态显示、控制界面 | React组件 | 380行 |
| **live2d.html** | PIXI.js集成、模型渲染 | 独立HTML页面 | 642行 |
| **Redux Store** | 全局状态管理、数据持久化 | Redux Toolkit | 104行 |
| **EventService** | AI响应事件、跨组件通信 | 事件总线 | +15行扩展 |

## 五、开发流程

1. **阅读官方文档**: 务必完整阅读 `docs/` 目录下的相关文档
2. **参考现有代码**: 研究项目中类似功能的实现模式
3. **完整阅读目标文件**: 修改文件内容前，完整的阅读要修改的文件
4. **不要遗漏**：务必检查你是否修改了所有需要修改的文件
5. **及时记录**: 所有文件增删都必须在此文件里同步